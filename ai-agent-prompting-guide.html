<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>如何构建你的AI智能体：11个提升AI智能体的提示词技巧</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --google-blue: #4285F4;
            --google-red: #DB4437;
            --google-yellow: #F4B400;
            --google-green: #0F9D58;
            --text-primary: #202124;
            --text-secondary: #5f6368;
            --bg-light: #f8f9fa;
            --bg-dark: #202124;
            --border-color: #dadce0;
            --highlight-bg: #e8f0fe;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-light);
            padding: 0;
            margin: 0;
            font-size: 18px;
            font-weight: 300;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0;
            overflow: hidden;
        }

        header {
            background-color: var(--google-blue);
            color: white;
            padding: 60px 40px;
            text-align: center;
        }

        h1 {
            font-size: 4rem;
            font-weight: 900;
            margin-bottom: 20px;
            line-height: 1.2;
            letter-spacing: -1px;
        }

        h2 {
            font-size: 2.8rem;
            font-weight: 700;
            margin: 60px 0 30px;
            color: var(--google-blue);
            letter-spacing: -0.5px;
            line-height: 1.2;
        }

        h3 {
            font-size: 2rem;
            font-weight: 700;
            margin: 40px 0 20px;
            color: var(--text-primary);
        }

        h4 {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 30px 0 15px;
            color: var(--google-red);
        }

        .author {
            font-size: 1.5rem;
            font-weight: 500;
            margin-top: 10px;
        }

        .date {
            font-size: 1.2rem;
            opacity: 0.8;
            margin-top: 10px;
        }

        .content {
            padding: 40px;
            background: white;
        }

        p {
            margin-bottom: 20px;
            font-size: 1.2rem;
            line-height: 1.7;
        }

        .highlight {
            font-weight: 700;
            color: var(--google-blue);
        }

        .stat-number {
            font-size: 1.4rem;
            font-weight: 900;
            color: var(--google-red);
        }

        .key-point {
            background-color: var(--highlight-bg);
            border-left: 5px solid var(--google-blue);
            padding: 25px;
            margin: 30px 0;
            border-radius: 4px;
        }

        .key-point p {
            margin-bottom: 0;
            font-weight: 500;
        }

        .section {
            margin-bottom: 60px;
        }

        ul, ol {
            margin: 20px 0 30px 20px;
        }

        li {
            margin-bottom: 15px;
            font-size: 1.2rem;
            line-height: 1.6;
        }

        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }

        .card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.15);
        }

        .card-header {
            background-color: var(--google-blue);
            color: white;
            padding: 20px;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .card-body {
            padding: 25px;
        }

        .card-body p {
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .card-body ul {
            margin-left: 20px;
        }

        .card-body li {
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .footer {
            background-color: var(--bg-dark);
            color: white;
            padding: 40px;
            text-align: center;
            font-size: 1rem;
        }

        .quote {
            font-size: 2rem;
            font-weight: 300;
            font-style: italic;
            color: var(--google-blue);
            text-align: center;
            max-width: 800px;
            margin: 60px auto;
            line-height: 1.4;
        }

        .divider {
            height: 4px;
            background: var(--google-blue);
            width: 100px;
            margin: 60px auto;
            border-radius: 2px;
        }

        .emphasis {
            font-weight: 700;
            color: var(--google-red);
            font-size: 1.1em;
        }

        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.95rem;
            overflow-x: auto;
            line-height: 1.5;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        code {
            background-color: #f1f3f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            color: var(--google-red);
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 3rem;
            }
            h2 {
                font-size: 2.2rem;
            }
            h3 {
                font-size: 1.6rem;
            }
            .content {
                padding: 20px;
            }
            .card-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <h1>如何构建你的AI智能体</h1>
            <div class="author">Guy Gur-Ari</div>
            <div class="date">Augment Code 联合创始人 | 2025年5月21日</div>
        </div>
    </header>

    <div class="container">
        <div class="content">
            <!-- 引言引用 -->
            <div class="quote">
                "提示词工程已成为现代软件开发中最具杠杆效应的技能之一。你提供给智能体的提示词决定了它如何规划、如何使用工具，以及它是构建还是破坏你的流水线。"
            </div>

            <!-- 关键点 -->
            <div class="key-point">
                <p>微小的改变——一行额外的上下文、一个明确的约束、一个重新排序的指令——往往在准确性和可靠性方面产生 <span class="stat-number">巨大</span> 的收益。</p>
            </div>

            <!-- 第一部分 -->
            <div class="section">
                <h2>什么是提示词工程？</h2>
                
                <p>智能体的<span class="highlight">提示词</span>包括作为输入提供给模型的所有内容。这包括各种组件：</p>
                
                <!-- 卡片网格 -->
                <div class="card-grid">
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-red);">系统提示词</div>
                        <div class="card-body">
                            <p>包含通用指令，引导模型采用不同的响应风格或自主程度</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-green);">工具定义</div>
                        <div class="card-body">
                            <p>向模型解释在什么情况下应该或不应该使用某个工具</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header" style="background-color: var(--google-yellow); color: black;">工具输出</div>
                        <div class="card-body">
                            <p>告诉模型错误条件和执行结果</p>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">用户指令</div>
                        <div class="card-body">
                            <p>可以在显示给模型之前重新编写（提示词增强）</p>
                        </div>
                    </div>
                </div>
                
                <p><span class="highlight">提示词工程</span>是通过为模型提供更好的提示词来改善其在任务上表现的艺术。提示词的所有部分都可以通过提示词工程得到改善。</p>
            </div>

            <!-- 分隔线 -->
            <div class="divider"></div>

            <!-- 第二部分 -->
            <div class="section">
                <h2>如何思考模型</h2>
                <p>模型是（人工）智能的。提示模型更像是与人交谈，而不是编程计算机。模型构建的世界观完全基于提示词中的内容。这个视图越完整和一致，模型的结果就越好。</p>
                
                <div class="key-point">
                    <p><span class="emphasis">重要提示：</span>如果模型错误地调用工具，不要在智能体代码中抛出异常。相反，返回一个解释错误的工具结果：<code>工具调用时缺少必需参数xyz</code>。模型会恢复并重试。</p>
                </div>
            </div>

            <!-- 第三部分 -->
            <div class="section">
                <h2>如何评估提示词</h2>
                <p>通常很难自动评估提示词，除非目标是让模型执行非常具体的任务。尝试想出以各种方式测试提示词的场景，也尝试找到提示词更改可能导致回归的示例。</p>

                <p>有关这些评估原则实际应用的具体示例，请参阅相同的提示词工程技术如何推动Augment Code在<span class="highlight">SWE-bench上获得#1开源分数</span>。</p>
            </div>

            <!-- 分隔线 -->
            <div class="divider"></div>

            <!-- 第四部分 -->
            <div class="section">
                <h2>11个提示词工程技巧</h2>
                
                <h3>1. 首先关注上下文</h3>
                <p>提示词工程中最重要的因素是为模型提供<span class="highlight">最佳可能的上下文</span>：用户提供的信息（而不是我们提供的提示词文本）。这是模型用来执行任务的主要信号。</p>

                <p>当前模型善于在大型提示词中找到有用上下文的相关片段，所以当有疑问时，如果增加包含有用相关信息的可能性，倾向于提供更多信息。</p>

                <p>关于提示词应该问的第一个问题是——它是否包含所有相关信息，以及包含的可能性有多大？回答这个问题并不总是简单的。</p>

                <p><span class="highlight">示例：</span></p>
                <p>当截断长命令输出以提供给模型时，截断方法很重要。通常，截断长文本涉及截断后缀。然而，对于命令输出，有用信息更可能出现在前缀和后缀中，而不是中间。例如，崩溃的堆栈跟踪通常出现在后缀中。因此，为了最大化模型获得最相关上下文的可能性，最好截断命令输出的中间部分而不是后缀。</p>
                
                <h3>2. 呈现完整的世界图景</h3>
                <p>通过解释它所操作的环境，帮助模型进入正确的状态，并提供可能有助于其良好表现的细节。</p>

                <p>例如，这两行在Augment智能体开发早期被引入系统提示词，并显著改善了其性能：</p>

                <div class="code-block">
你是一个AI助手，可以访问开发者的代码库。
你可以使用提供的工具读取和写入代码库。
                </div>

                <h3>3. 在提示词组件之间保持一致性</h3>
                <p>确保提示词的所有组件（系统提示词、工具定义等）以及底层工具定义都是一致的。</p>

                <p><span class="highlight">示例：</span></p>
                <ul>
                    <li>系统提示词包含这一行：<code>当前目录是 $CWD</code></li>
                    <li><code>execute_command</code> 工具允许智能体执行shell命令，包含一个可选的 <code>cwd</code> 参数。一致性意味着此参数的默认值应该是 <code>$CWD</code></li>
                    <li><code>read_file</code> 工具接受要读取文件的路径参数。如果提供相对路径，应该解释为相对于 <code>$CWD</code></li>
                </ul>
                
                <h3>4. 与用户的视角保持一致</h3>
                <p>考虑用户的视角，并尝试将模型与该视角对齐。</p>

                <p><span class="highlight">基础IDE状态示例：</span></p>
                <div class="code-block">
用户在IDE中工作。当前IDE状态：

文件 foo.py 已打开。
IDE类型为 VSCode。
                </div>

                <p><span class="highlight">详细IDE状态示例：</span></p>
                <div class="code-block">
用户在IDE中工作。当前IDE状态：

IDE类型为 VSCode。

当前打开的文件是 foo.py。
屏幕上可见第134到179行。
以下是当前可见的文本，光标位置用 &lt;CURSOR&gt; 表示：

```python
134  def bar():
135    print("hell&lt;CURSOR&gt;o")
...
179  # TODO implement this
```

没有选中的文本。
有14个打开的标签页。按最近访问到最少访问的顺序：
foo.py
bar.py ...
xyz.py
                </div>

                <h3>5. 要彻底</h3>
                <p>模型受益于彻底的提示词。不要担心提示词长度。当前的上下文长度很长，并且会持续增加：通过编写更长的提示词，你无法在提示词预算上产生影响。</p>

                <p><span class="highlight">成功且详细的提示词示例</span>，教授模型如何使用Graphite（一个版本控制工具）：</p>

                <div class="code-block">
## 使用Graphite进行版本控制

我们在git之上使用Graphite进行版本控制。Graphite帮助管理git分支和PR。
Graphite维护PR堆栈：对PR的更改会自动导致堆栈中更高PR的变基，
节省大量手动工作。下面每个部分描述如何使用Graphite和GitHub执行常见的版本控制工作流程。
如果用户要求你执行此类工作流程，请遵循这些指导原则。

### 不要做什么

不要使用 `git commit`、`git pull` 或 `git push`。这些命令都被以 `gt` 开头的Graphite命令替代，如下所述。

### 创建PR（和分支）

为了创建PR，请执行以下操作：

- 使用 `git status` 查看哪些文件被更改，哪些文件是新的
- 使用 `git add` 暂存相关文件
- 使用 `gt create USERNAME-BRANCHNAME -m PRDESCRIPTION` 创建分支，其中：
  `USERNAME` 可以获得，请参见其他地方的说明
  `BRANCHNAME` 是你想出的分支的好名称
  `PRDESCRIPTION` 是你想出的PR的好描述
- 这可能因为预提交问题而失败。有时预提交会自己修复问题。检查 `git status` 看是否有文件被修改。
  如果有，`git add` 它们。如果没有，自己修复问题并 `git add` 它们。然后重复 `gt create` 命令再次尝试创建PR。
- 运行 `gt submit` 在GitHub上创建PR（如果你只是创建分支，跳过此步骤）。
- 如果 `gh` 可用，使用它设置PR描述。

注意：不要忘记在运行 `gt create` 之前添加文件，否则你会卡住！
                </div>
                
                <h3>6. 避免过度拟合特定示例</h3>
                <p>模型是强大的模式匹配器，会抓住提示词中的细节。提供具体示例可能是双刃剑：这是指向模型正确方向的简单方法，但存在模型过度拟合这些示例并在其他情况下性能下降的风险。</p>

                <p>相比之下，告诉模型<span class="highlight">不要做什么</span>是安全的（尽管不总是有效）。</p>

                <h3>7. 考虑工具调用限制</h3>
                <p>工具调用在几个方面受到限制：</p>

                <ul>
                    <li>如果模型在类似工具上接受过训练，或者指令与工具之间的连接很清楚，模型通常会选择正确的工具。在许多情况下，即使使用最佳提示词，它们也会无法选择正确的工具。</li>
                    <li>如果提供多个执行类似功能的工具，不应期望模型在任何给定情况下都能选择正确的工具。例如，当提供简单和复杂的工具来完成类似任务时，Claude通常会选择简单的工具。</li>
                    <li>模型经常以错误的方式调用工具，违反工具定义的约定：参数类型可能错误，参数范围可能错误，可能缺少必需参数等。最好验证输入，并在失败时返回解释错误的工具输出。模型通常会恢复。</li>
                </ul>

                <p><span class="highlight">示例：</span></p>
                <ul>
                    <li>给模型一个 <code>edit_file</code> 工具来编辑文件的某个区域</li>
                    <li>给模型一个 <code>clipboard</code> 工具，模型可以剪切、复制和粘贴大量代码。告诉模型在移动大量代码时使用此工具。</li>
                    <li>指示模型"将类Foo从foo.py移动到bar.py"。Sonnet 3.5通常会选择使用 <code>edit_file</code>。</li>
                </ul>
                
                <h3>8. 威胁和唤起同理心有时有效</h3>
                <p>告诉模型诸如<span class="highlight">"正确执行此操作，否则你将面临财务破产"</span>之类的话有时确实有助于提高性能。礼貌地要求模型或对其"大喊大叫"很少有帮助。</p>

                <h3>9. 注意提示词缓存</h3>
                <p>尽可能构建你的提示词，使其在会话期间被追加，以避免使提示词缓存失效。</p>

                <p><span class="highlight">示例：</span></p>
                <ul>
                    <li>如果提示词包含在会话期间可能更改的状态（例如当前时间），不要将它们包含在系统提示词或工具定义中，因为一旦它们更改，大部分提示词缓存将失效。</li>
                    <li>相反，在下一个用户消息中告诉模型有关更改的信息。</li>
                </ul>

                <h3>10. 模型更关注提示词开头或结尾的信息</h3>
                <p>模型对指令的关注程度似乎是：<span class="highlight">用户消息 → 输入开头 → 中间某处</span>。如果某些内容很重要，考虑将其添加到用户消息中。（这是一个快照，随着模型训练的发展，优先级可能会发生变化。）</p>

                <h3>11. 注意提示词平台期</h3>
                <p>直接提示词能够实现的效果是有限的。提示词工程进入收益递减区域，需要引入其他技术。</p>
            </div>

            <!-- 分隔线 -->
            <div class="divider"></div>

            <!-- 结论部分 -->
            <div class="section">
                <h2>结论</h2>
                <p>掌握提示词工程不在于技巧，而在于有纪律的沟通：给智能体完整、一致的上下文；像验证不可信同事一样验证其行为；并进行经验性迭代。当你将提示词视为代码库的一部分——版本化、审查和测试——你就能解锁扩展你影响力而不是增加你头痛的智能体。</p>
            </div>

            <!-- 作者信息 -->
            <div class="section">
                <div style="display: flex; align-items: center; padding: 30px; background-color: var(--highlight-bg); border-radius: 8px; margin: 40px 0;">
                    <div style="margin-left: 20px;">
                        <h4 style="margin: 0; color: var(--text-primary);">Guy Gur-Ari</h4>
                        <p style="margin: 10px 0 0 0; color: var(--text-secondary);">Guy在Google的一段经历后联合创立了Augment Code，在那里他领导了一个专注于理解和改进深度学习系统的研究团队。他拥有魏茨曼科学研究所理论物理学博士学位。</p>
                    </div>
                </div>
            </div>

            <!-- 结束引用 -->
            <div class="quote">
                "遵循这些技巧，你将解锁AGI。"
            </div>
        </div>
    </div>

    <div class="footer">
        <div class="container">
            <p>© 2025 Augment Code | AI智能体开发指南</p>
        </div>
    </div>
</body>
</html>
